{
  "include": ["**/*.ts", "**/*.tsx"],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"],
      "~/components/*": ["./app/core/components/*"],
      "~/utils/*": ["./app/core/utils/*"],
      "~/user/*": ["./app/modules/user/*"],
      "~/auth/*": ["./app/modules/auth/*"],
      "~/category/*": ["./app/modules/category/*"],
      "~/brand/*": ["./app/modules/brand/*"],
      "~/measurement-unit/*": ["./app/modules/measurement-unit/*"],
      "~/container/*": ["./app/modules/container/*"],
      "~/product/*": ["./app/modules/product/*"],
      "~/person/*": ["./app/modules/person/*"],
      "~/work-area/*": ["./app/modules/work-area/*"],
      "~/operation/*": ["./app/modules/operation/*"],
      "~/production-flow/*": ["./app/modules/production-flow/*"],
    },

    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "module": "ESNext",
    "target": "ES2022",
    "skipLibCheck": true,
    "strictNullChecks": true,
  }
}
