import { Schema } from "effect";
import { WorkArea, CreateWorkArea, UpdateWorkArea } from "../../model/work-area";

export const WorkAreaApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const WorkAreaFromApi = Schema.transform(WorkAreaApi, WorkArea, {
	strict: true,
	decode: (workAreaApi) => ({
		...workAreaApi,
		createdAt: workAreaApi.created_at,
		updatedAt: workAreaApi.updated_at,
		deletedAt: workAreaApi.deleted_at,
	}),
	encode: (workArea) => ({
		...workArea,
		created_at: workArea.createdAt,
		updated_at: workArea.updatedAt,
		deleted_at: workArea.deletedAt,
	}),
});

export const WorkAreaListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(WorkAreaFromApi))),
	Schema.mutable(Schema.Array(WorkArea)),
	{
		strict: true,
		decode: (workAreaApiList) => (workAreaApiList ? workAreaApiList : []),
		encode: (workAreaList) => workAreaList,
	},
);

export const CreateWorkAreaApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
});

export const CreateWorkAreaApiFromCreateWorkArea = Schema.transform(
	CreateWorkArea,
	CreateWorkAreaApi,
	{
		strict: true,
		decode: (createWorkArea) => createWorkArea,
		encode: (createWorkAreaApi) => createWorkAreaApi,
	},
);

export const UpdateWorkAreaApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
});

export const UpdateWorkAreaApiFromUpdateWorkArea = Schema.transform(
	UpdateWorkArea,
	UpdateWorkAreaApi,
	{
		strict: true,
		decode: (updateWorkArea) => updateWorkArea,
		encode: (updateWorkAreaApi) => updateWorkAreaApi,
	},
);

export const CreateWorkAreaApiResponse = Schema.String;
