import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { CreateWorkArea, UpdateWorkArea } from "../../model/work-area";
import { WorkAreaRepository } from "../../model/repository";
import {
	WorkAreaFromApi,
	WorkAreaListFromApi,
	CreateWorkAreaApiFromCreateWorkArea,
	CreateWorkAreaApiResponse,
	UpdateWorkAreaApiFromUpdateWorkArea,
} from "./dto";

const baseUrl = "/v1/work-areas";

const makeWorkAreaApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(WorkAreaListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(WorkAreaFromApi))),
		create: (workArea: CreateWorkArea) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateWorkAreaApiFromCreateWorkArea)(workArea),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateWorkAreaApiResponse))),
		update: (workArea: UpdateWorkArea) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateWorkAreaApiFromUpdateWorkArea)(workArea),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate-code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const workAreaApiRepoLive = Layer.effect(WorkAreaRepository, makeWorkAreaApiRepo);
