import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { WorkArea, CreateWorkArea, UpdateWorkArea } from "./work-area";

export class WorkAreaUsecase extends Effect.Tag("WorkAreaUsecase")<
	WorkAreaUsecase,
	{
		readonly getAll: () => Effect.Effect<WorkArea[], AppError>;
		readonly getById: (id: string) => Effect.Effect<WorkArea, AppError>;
		readonly create: (workArea: CreateWorkArea) => Effect.Effect<string, AppError>;
		readonly update: (workArea: UpdateWorkArea) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
