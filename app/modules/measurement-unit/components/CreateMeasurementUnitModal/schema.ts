import * as v from "valibot";

export const CreateMeasurementUnitSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
});
export type CreateMeasurementUnitSchema = v.InferOutput<typeof CreateMeasurementUnitSchema>;
