import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { measurementUnitOptions } from "../../hooks/measurement-unit-options";
import Table from "./table";

export default function MeasurementUnitTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(measurementUnitOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table measurementUnits={data} />;
}
