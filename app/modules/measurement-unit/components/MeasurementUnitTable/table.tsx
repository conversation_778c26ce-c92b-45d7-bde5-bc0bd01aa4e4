import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { MeasurementUnit } from "../../service/model/measurement-unit";
import { columns } from "./columns";

interface Props {
	measurementUnits: MeasurementUnit[];
}

export default function Table({ measurementUnits }: Props) {
	const table = useReactTable({
		data: measurementUnits,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
