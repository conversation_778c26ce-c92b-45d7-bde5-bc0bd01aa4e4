import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import EditMeasurementUnitForm from "./EditMeasurementUnitForm";
import type { EditMeasurementUnitModalProps } from "./use-edit-measurement-unit-modal";

export default function EditMeasurementUnitModal({
	isOpen,
	setIsOpen,
	measurementUnit,
}: EditMeasurementUnitModalProps) {
	function handleClose() {
		setIsOpen(false);
	}

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Unidad de Medida</h3>
				<EditMeasurementUnitForm
					measurementUnit={measurementUnit}
					setIsOpen={setIsOpen}
				/>
			</div>
		</div>
	);
}
