import { Hash, Ruler } from "lucide-react";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { EditMeasurementUnitModalProps } from "./use-edit-measurement-unit-modal";
import useEditMeasurementUnitModal from "./use-edit-measurement-unit-modal";

export default function EditMeasurementUnitForm({
	measurementUnit,
	setIsOpen,
}: Omit<EditMeasurementUnitModalProps, "isOpen">) {
	const { measurementUnit: measurementUnitService } = useService();
	const { form } = useEditMeasurementUnitModal({
		isOpen: true,
		setIsOpen,
		measurementUnit,
	});

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<form.AppField
						name="name"
						children={({ FSTextField }) => (
							<FSTextField
								label="Nombre"
								placeholder="Nombre de la unidad de medida"
								prefixComponent={<Ruler size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="code"
						validators={{
							onChangeAsyncDebounceMs: 500,
							onChangeAsync: async ({ value }) => {
								if (!value || value.trim() === "" || value === measurementUnit.code) {
									return undefined;
								}
								try {
									await AppRuntime.runPromise(
										measurementUnitService.validateCode(value),
									);
									return undefined;
								} catch (e) {
									return [{ message: "El código ya existe" }];
								}
							},
						}}
						children={({ FSTextField }) => (
							<FSTextField
								label="Código"
								placeholder="Código de la unidad de medida"
								prefixComponent={<Hash size={16} />}
							/>
						)}
					/>
				</fieldset>
				<div className="modal-action">
					<form.SubscribeButton label="Actualizar" className="btn btn-primary" />
				</div>
			</form.AppForm>
		</form>
	);
}
