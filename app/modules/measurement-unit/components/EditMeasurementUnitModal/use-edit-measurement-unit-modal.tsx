import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useUpdateMeasurementUnit from "../../hooks/use-update-measurement-unit";
import type { MeasurementUnit } from "../../service/model/measurement-unit";
import { CreateMeasurementUnitSchema } from "../CreateMeasurementUnitModal/schema";

export interface EditMeasurementUnitModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	measurementUnit: MeasurementUnit;
}

export default function useEditMeasurementUnitModal({
	setIsOpen,
	measurementUnit,
}: EditMeasurementUnitModalProps) {
	const { mutate } = useUpdateMeasurementUnit();

	const form = useAppForm({
		defaultValues: {
			name: measurementUnit.name,
			code: measurementUnit.code,
		} as CreateMeasurementUnitSchema,
		validators: {
			onChange: CreateMeasurementUnitSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: measurementUnit.id,
					name: value.name,
					code: value.code,
				},
				{
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
					onSettled: () => {
						toast.success("Unidad de medida actualizada");
						handleClose();
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
