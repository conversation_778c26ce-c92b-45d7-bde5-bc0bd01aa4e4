import { Plus } from "lucide-react";
import { useState } from "react";
import CreateMeasurementUnitModal from "../CreateMeasurementUnitModal";
import MeasurementUnitTable from "../MeasurementUnitTable";

export default function MeasurementUnitManagement() {
	const [isCreateOpen, setIsCreateOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto max-w-4xl">
				<div className="card bg-base-300 shadow-xl">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsCreateOpen(true)}
							>
								<Plus size={16} />
								Nueva Unidad de Medida
							</button>
						</div>

						<MeasurementUnitTable />
					</div>
				</div>
			</div>

			<CreateMeasurementUnitModal
				isOpen={isCreateOpen}
				setIsOpen={setIsCreateOpen}
			/>
		</>
	);
}
