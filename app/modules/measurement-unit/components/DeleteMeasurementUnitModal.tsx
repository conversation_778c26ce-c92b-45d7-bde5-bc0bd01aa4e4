import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import useDeleteMeasurementUnit from "../hooks/use-delete-measurement-unit";
import type { MeasurementUnit } from "../service/model/measurement-unit";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	measurementUnit: MeasurementUnit;
}

export default function DeleteMeasurementUnitModal({
	isOpen,
	setIsOpen,
	measurementUnit,
}: Props) {
	const { mutate } = useDeleteMeasurementUnit();

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar unidad de medida</h3>
				<p>¿Estás seguro de que quieres eliminar esta unidad de medida?</p>
				<p className="mt-2 text-gray-600 text-sm">
					Unidad de medida: {measurementUnit.name} ({measurementUnit.code})
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-primary"
						onClick={() => setIsOpen(false)}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={() => {
							mutate(measurementUnit.id, {
								onError: (error) => {
									console.log(error);
									toast.error("Error al eliminar unidad de medida");
								},
								onSettled: () => {
									toast.success("Unidad de medida eliminada");
									setIsOpen(false);
								},
							});
						}}
					>
						Eliminar
					</button>
				</div>
			</div>
		</div>
	);
}
