import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";

import { measurementUnitOptions } from "./measurement-unit-options";

export default function useDeleteMeasurementUnit() {
	const service = useService();
	const { measurementUnit } = service;
	const queryClient = useQueryClient();
	const queryKey = measurementUnitOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-measurement-unit"],
		mutationFn: (id: string) => AppRuntime.runPromise(measurementUnit.delete(id)),
		onMutate: async (deletedId) => {
			await queryClient.cancelQueries({ queryKey });

			const previousMeasurementUnits = queryClient.getQueryData(queryKey);

			if (previousMeasurementUnits) {
				queryClient.setQueryData(
					queryKey,
					create(previousMeasurementUnits, (draft) => {
						const index = draft.findIndex((item) => item.id === deletedId);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousMeasurementUnits };
		},
		onError: (_err, _deletedId, context) => {
			queryClient.setQueryData(queryKey, context?.previousMeasurementUnits);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
