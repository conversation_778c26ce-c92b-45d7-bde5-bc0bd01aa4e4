import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const measurementUnitOptions = ({ measurementUnit }: serviceRegistry) =>
	queryOptions({
		queryKey: ["measurement-units"],
		queryFn: () => AppRuntime.runPromise(measurementUnit.getAll()),
	});

export const measurementUnitOptionsById = ({ measurementUnit }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["measurement-units", id],
		queryFn: () => AppRuntime.runPromise(measurementUnit.getById(id)),
	});
