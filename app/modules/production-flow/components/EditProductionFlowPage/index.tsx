import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { ArrowLeft, Hash, Plus, Tag } from "lucide-react";
import { useEffect, useState } from "react";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { getErrorResult } from "~/core/utils/effectErrors";
import { productionFlowWithActivitiesOptionsById } from "../../hooks/production-flow-options";
import ActivitiesTable from "../ActivitiesTable";
import AddActivityModal from "../AddActivityModal";
import type { ActivitySchema } from "../schemas";
import useEditProductionFlowPage from "./use-edit-production-flow-page";

interface EditProductionFlowPageProps {
	id: string;
}

export default function EditProductionFlowPage({
	id,
}: EditProductionFlowPageProps) {
	const service = useService();
	const { productionFlow } = service;
	const [isAddActivityOpen, setIsAddActivityOpen] = useState(false);
	const [activities, setActivities] = useState<ActivitySchema[]>([]);

	// Fetch production flow with activities
	const { data, isLoading, error } = useQuery(
		productionFlowWithActivitiesOptionsById(service, id),
	);

	const { form, handleSubmit, isPending, isInitialized } =
		useEditProductionFlowPage({
			id,
			productionFlowData: data,
			activities,
			setActivities,
		});

	const handleAddActivity = (activity: ActivitySchema) => {
		setActivities((prev) => [...prev, activity]);
	};

	const handleReorderActivities = (reorderedActivities: ActivitySchema[]) => {
		setActivities(reorderedActivities);
	};

	const handleDeleteActivity = (tempId: string) => {
		setActivities((prev) =>
			prev.filter((activity) => activity.tempId !== tempId),
		);
	};

	const nextIndexNumber = activities.length + 1;

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isLoading) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	if (!data || !isInitialized) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/manufacture/production-flow"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Editar Flujo de Producción</h1>
				</div>
			</div>

			<div className="space-y-6">
				{/* Production Flow Form */}
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información del Flujo</h2>
						<form.AppForm>
							<fieldset className="fieldset">
								<form.AppField
									name="name"
									children={({ FSTextField }) => (
										<FSTextField
											label="Nombre"
											placeholder="Nombre del flujo de producción"
											prefixComponent={<Tag size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="code"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (
												!value ||
												value.trim() === "" ||
												value === data.productionFlow.code
											) {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(
													productionFlow.validateCode(value),
												);
												return undefined;
											} catch (e) {
												return [{ message: "El código ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Código"
											placeholder="Código del flujo de producción"
											prefixComponent={<Hash size={16} />}
										/>
									)}
								/>
							</fieldset>
						</form.AppForm>
					</div>
				</div>

				{/* Activities Section */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h2 className="font-semibold text-xl">Actividades</h2>
						<button
							type="button"
							className="btn btn-primary btn-sm"
							onClick={() => setIsAddActivityOpen(true)}
						>
							<Plus size={16} />
							Agregar Actividad
						</button>
					</div>

					<ActivitiesTable
						activities={activities}
						onReorder={handleReorderActivities}
						onDelete={handleDeleteActivity}
					/>
				</div>

				{/* Submit Button */}
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<div className="flex justify-end gap-4">
							<Link
								to="/admin/manufacture/production-flow"
								className="btn btn-ghost"
							>
								Cancelar
							</Link>
							<button
								type="button"
								className="btn btn-primary"
								onClick={handleSubmit}
								disabled={isPending || activities.length === 0}
							>
								{isPending ? (
									<span className="loading loading-spinner loading-sm" />
								) : (
									"Actualizar Flujo de Producción"
								)}
							</button>
						</div>
						{activities.length === 0 && (
							<p className="mt-2 text-error text-sm">
								Debe tener al menos una actividad en el flujo de producción.
							</p>
						)}
					</div>
				</div>
			</div>

			<AddActivityModal
				isOpen={isAddActivityOpen}
				setIsOpen={setIsAddActivityOpen}
				onAddActivity={handleAddActivity}
				nextIndexNumber={nextIndexNumber}
			/>
		</div>
	);
}
