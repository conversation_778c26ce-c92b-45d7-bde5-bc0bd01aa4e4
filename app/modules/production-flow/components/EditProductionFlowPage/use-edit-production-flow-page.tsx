import { useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useUpdateProductionFlow from "../../hooks/use-update-production-flow";
import type { ProductionFlowWithActivities } from "../../service/model/production-flow";
import type { ActivitySchema } from "../schemas";
import { ProductionFlowSchema } from "../schemas";

interface UseEditProductionFlowPageProps {
	id: string;
	productionFlowData?: ProductionFlowWithActivities;
	activities: ActivitySchema[];
	setActivities: React.Dispatch<React.SetStateAction<ActivitySchema[]>>;
}

export default function useEditProductionFlowPage({
	id,
	productionFlowData,
	activities,
	setActivities,
}: UseEditProductionFlowPageProps) {
	const navigate = useNavigate();
	const { mutate, isPending } = useUpdateProductionFlow();
	const [isInitialized, setIsInitialized] = useState(false);

	const form = useAppForm({
		defaultValues: {
			name: productionFlowData?.productionFlow.name || "",
			code: productionFlowData?.productionFlow.code || "",
		} as ProductionFlowSchema,
		validators: {
			onChange: ProductionFlowSchema,
		},
		onSubmit: ({ value }) => {
			// This will be handled by the handleSubmit function
		},
	});

	// Initialize form and activities when data is loaded
	useEffect(() => {
		if (productionFlowData && !isInitialized) {
			// Transform activities to local format - Updated for new flattened structure
			const transformedActivities: ActivitySchema[] =
				productionFlowData.activities.map((activityDetail, index) => ({
					workAreaId: activityDetail.workArea.id,
					operationId: activityDetail.operation.id,
					indexNumber: activityDetail.indexNumber,
					workAreaName: `${activityDetail.workArea.code} - ${activityDetail.workArea.name}`,
					operationName: `${activityDetail.operation.code} - ${activityDetail.operation.name}`,
					tempId: `existing-${activityDetail.id}`,
				}));

			setActivities(transformedActivities);
			setIsInitialized(true);
		}
	}, [productionFlowData, isInitialized, setActivities]);

	const handleSubmit = () => {
		// Validate form first
		form.handleSubmit();

		// Check if form is valid
		if (form.state.errors.length > 0) {
			toast.error("Por favor corrige los errores en el formulario");
			return;
		}

		// Check if activities are present
		if (activities.length === 0) {
			toast.error("Debe tener al menos una actividad");
			return;
		}

		// Get form values
		const formValues = form.state.values;

		// For now, we'll just update the basic production flow info
		// In a real implementation, you might want to handle activities updates separately
		mutate(
			{
				id,
				name: formValues.name,
				code: formValues.code,
			},
			{
				onSuccess: () => {
					toast.success("Flujo de producción actualizado exitosamente");
					navigate({ to: "/admin/manufacture/production-flow" });
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			},
		);
	};

	return {
		form,
		handleSubmit,
		isPending,
		isInitialized,
	};
}
