import {
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	restrictToParentElement,
	restrictToVerticalAxis,
} from "@dnd-kit/modifiers";
import {
	SortableContext,
	arrayMove,
	sortableKeyboardCoordinates,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import type { ActivitySchema } from "../schemas";
import SortableActivityRow from "./SortableActivityRow";

interface ActivitiesTableProps {
	activities: ActivitySchema[];
	onReorder: (activities: ActivitySchema[]) => void;
	onDelete: (tempId: string) => void;
}

export default function ActivitiesTable({
	activities,
	onReorder,
	onDelete,
}: ActivitiesTableProps) {
	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			const oldIndex = activities.findIndex(
				(item) => item.tempId === active.id,
			);
			const newIndex = activities.findIndex((item) => item.tempId === over.id);

			const reorderedActivities = arrayMove(activities, oldIndex, newIndex);

			// Update index numbers
			const updatedActivities = reorderedActivities.map((activity, index) => ({
				...activity,
				indexNumber: index + 1,
			}));

			onReorder(updatedActivities);
		}
	};

	if (activities.length === 0) {
		return (
			<div className="card bg-base-100 shadow-sm">
				<div className="card-body">
					<p className="text-center text-gray-500">
						No hay actividades agregadas. Haz clic en "Agregar Actividad" para
						comenzar.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="card bg-base-100 shadow-sm">
			<div className="card-body">
				<h3 className="card-title">Actividades</h3>
				<div className="overflow-x-auto">
					<table className="table">
						<thead>
							<tr>
								<th>Orden</th>
								<th>Área de Trabajo</th>
								<th>Operación</th>
								<th>Acciones</th>
							</tr>
						</thead>
						<tbody>
							<DndContext
								sensors={sensors}
								collisionDetection={closestCenter}
								onDragEnd={handleDragEnd}
								modifiers={[restrictToVerticalAxis, restrictToParentElement]}
							>
								<SortableContext
									items={activities.map((activity) => activity.tempId || "")}
									strategy={verticalListSortingStrategy}
								>
									{activities.map((activity) => (
										<SortableActivityRow
											key={activity.tempId}
											activity={activity}
											onDelete={onDelete}
										/>
									))}
								</SortableContext>
							</DndContext>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
}
