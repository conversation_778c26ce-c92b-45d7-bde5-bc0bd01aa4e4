import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash, Eye } from "lucide-react";
import { useState } from "react";
import { Link } from "@tanstack/react-router";
import type { ProductionFlow } from "../../service/model/production-flow";
import DeleteProductionFlowModal from "../DeleteProductionFlowModal";

const columnHelper = createColumnHelper<ProductionFlow>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => info.getValue(),
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const productionFlow = row.original;

			return (
				<div className="flex gap-2">
					<Link
						to="/admin/manufacture/production-flow/edit/$id"
						params={{ id: productionFlow.id }}
						className="btn btn-sm btn-primary"
					>
						<Edit size={16} />
					</Link>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<DeleteProductionFlowModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						productionFlow={productionFlow}
					/>
				</div>
			);
		},
	}),
];
