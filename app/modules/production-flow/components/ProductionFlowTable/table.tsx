import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { ProductionFlow } from "../../service/model/production-flow";
import { columns } from "./columns";

interface Props {
	productionFlows: ProductionFlow[];
}

export default function Table({ productionFlows }: Props) {
	const table = useReactTable({
		data: productionFlows,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
