import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import useDeleteProductionFlow from "../../hooks/use-delete-production-flow";
import type { ProductionFlow } from "../../service/model/production-flow";

interface DeleteProductionFlowModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	productionFlow: ProductionFlow;
}

export default function DeleteProductionFlowModal({
	isOpen,
	setIsOpen,
	productionFlow,
}: DeleteProductionFlowModalProps) {
	const { mutate, isPending } = useDeleteProductionFlow();

	const handleDelete = () => {
		mutate(productionFlow.id, {
			onSuccess: () => {
				toast.success("Flujo de producción eliminado");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar Flujo de Producción</h3>
				<p className="py-4">
					¿Estás seguro de que deseas eliminar el flujo de producción{" "}
					<strong>{productionFlow.name}</strong>? Esta acción no se puede
					deshacer.
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? (
							<span className="loading loading-spinner loading-sm" />
						) : (
							"Eliminar"
						)}
					</button>
				</div>
			</div>
		</div>
	);
}
