import { useQuery } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { operationOptions } from "~/modules/operation/hooks/operation-options";
import { workAreaOptions } from "~/modules/work-area/hooks/work-area-options";
import type { ActivitySchema } from "../schemas";
import { AddActivitySchema } from "../schemas";

export interface AddActivityModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	onAddActivity: (activity: ActivitySchema) => void;
	nextIndexNumber: number;
}

const defaultValues = {
	workAreaId: "",
	operationId: "",
} as AddActivitySchema;

export default function useAddActivityModal({
	setIsOpen,
	onAddActivity,
	nextIndexNumber,
}: AddActivityModalProps) {
	const service = useService();

	const { data: workAreas = [] } = useQuery(workAreaOptions(service));
	const { data: operations = [] } = useQuery(operationOptions(service));

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: AddActivitySchema,
		},
		onSubmit: ({ value }) => {
			console.log(value);
			const selectedWorkArea = workAreas.find(
				(wa) => wa.id === value.workAreaId,
			);
			const selectedOperation = operations.find(
				(op) => op.id === value.operationId,
			);

			if (!selectedWorkArea || !selectedOperation) {
				toast.error(
					"Error al encontrar el área de trabajo o la operación seleccionada",
				);
				return;
			}

			const newActivity: ActivitySchema = {
				workAreaId: value.workAreaId,
				operationId: value.operationId,
				indexNumber: nextIndexNumber,
				workAreaName: `${selectedWorkArea.code} - ${selectedWorkArea.name}`,
				operationName: `${selectedOperation.code} - ${selectedOperation.name}`,
				tempId: `temp-${Date.now()}-${Math.random()}`, // Unique temporary ID
			};

			onAddActivity(newActivity);
			toast.success("Actividad agregada");
			handleClose();
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending: false, // No async operation in this modal
	};
}
