import { useQuery } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { operationOptions } from "~/modules/operation/hooks/operation-options";
import { workAreaOptions } from "~/modules/work-area/hooks/work-area-options";
import type { ActivitySchema } from "../schemas";
import useAddActivityModal from "./use-add-activity-modal";

interface AddActivityModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	onAddActivity: (activity: ActivitySchema) => void;
	nextIndexNumber: number;
}

export default function AddActivityModal({
	isOpen,
	setIsOpen,
	onAddActivity,
	nextIndexNumber,
}: AddActivityModalProps) {
	const service = useService();
	const { form, handleClose, isPending } = useAddActivityModal({
		isOpen,
		setIsOpen,
		onAddActivity,
		nextIndexNumber,
	});

	const { data: workAreas = [], isLoading: isLoadingWorkAreas } = useQuery(
		workAreaOptions(service),
	);
	const { data: operations = [], isLoading: isLoadingOperations } = useQuery(
		operationOptions(service),
	);

	const workAreaComboOptions = workAreas.map((workArea) => ({
		value: workArea.id,
		label: `${workArea.code} - ${workArea.name}`,
	}));

	const operationComboOptions = operations.map((operation) => ({
		value: operation.id,
		label: `${operation.code} - ${operation.name}`,
	}));

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Agregar Actividad</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="workAreaId"
								children={({ FSComboBoxField }) => (
									<FSComboBoxField
										label="Área de Trabajo"
										placeholder="Seleccionar área de trabajo"
										options={workAreaComboOptions}
										isLoading={isLoadingWorkAreas}
									/>
								)}
							/>
							<form.AppField
								name="operationId"
								children={({ FSComboBoxField }) => (
									<FSComboBoxField
										label="Operación"
										placeholder="Seleccionar operación"
										options={operationComboOptions}
										isLoading={isLoadingOperations}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<button
								type="button"
								className="btn"
								onClick={handleClose}
								disabled={isPending}
							>
								Cancelar
							</button>
							<form.SubscribeButton
								className="btn btn-primary"
								isDisabled={isPending}
								label="Agregar"
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
