import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type {
	ProductionFlow,
	CreateProductionFlow,
	UpdateProductionFlow,
	ProductionFlowCreateWithActivities,
	ProductionFlowWithActivities,
} from "./production-flow";

export class ProductionFlowRepository extends Effect.Tag("ProductionFlowRepository")<
	ProductionFlowRepository,
	{
		readonly getAll: () => Effect.Effect<ProductionFlow[], AppError>;
		readonly getById: (id: string) => Effect.Effect<ProductionFlow, AppError>;
		readonly create: (productionFlow: CreateProductionFlow) => Effect.Effect<string, AppError>;
		readonly createWithActivities: (productionFlow: ProductionFlowCreateWithActivities) => Effect.Effect<string, AppError>;
		readonly update: (productionFlow: UpdateProductionFlow) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly getWithActivities: (id: string) => Effect.Effect<ProductionFlowWithActivities, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
