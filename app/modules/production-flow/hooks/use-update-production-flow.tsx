import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { ProductionFlow, UpdateProductionFlow } from "../service/model/production-flow";
import { productionFlowOptions } from "./production-flow-options";

export default function useUpdateProductionFlow() {
	const service = useService();
	const { productionFlow } = service;
	const queryClient = useQueryClient();
	const queryKey = productionFlowOptions(service).queryKey;

	return useMutation({
		mutationKey: ["update-production-flow"],
		mutationFn: (updateProductionFlow: UpdateProductionFlow) =>
			AppRuntime.runPromise(productionFlow.update(updateProductionFlow)),
		onMutate: async (updateProductionFlow) => {
			await queryClient.cancelQueries({ queryKey });

			const previousProductionFlows = queryClient.getQueryData(queryKey);

			if (previousProductionFlows) {
				queryClient.setQueryData(
					queryKey,
					create(previousProductionFlows, (draft) => {
						const index = draft.findIndex((pf) => pf.id === updateProductionFlow.id);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								name: updateProductionFlow.name,
								code: updateProductionFlow.code,
								updatedAt: new Date().toISOString(),
							};
						}
					}),
				);
			}

			return { previousProductionFlows };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousProductionFlows);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
