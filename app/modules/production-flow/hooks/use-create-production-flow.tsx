import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { ProductionFlow, CreateProductionFlow } from "../service/model/production-flow";
import { productionFlowOptions } from "./production-flow-options";

export default function useCreateProductionFlow() {
	const service = useService();
	const { productionFlow } = service;
	const queryClient = useQueryClient();
	const queryKey = productionFlowOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-production-flow"],
		mutationFn: (newProductionFlow: CreateProductionFlow) =>
			AppRuntime.runPromise(productionFlow.create(newProductionFlow)),
		onMutate: async (newProductionFlow) => {
			await queryClient.cancelQueries({ queryKey });

			const previousProductionFlows = queryClient.getQueryData(queryKey);

			if (previousProductionFlows) {
				queryClient.setQueryData(
					queryKey,
					create(previousProductionFlows, (draft) => {
						draft.push({
							id: "new",
							name: newProductionFlow.name,
							code: newProductionFlow.code,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						} as ProductionFlow);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					{
						id: "new",
						name: newProductionFlow.name,
						code: newProductionFlow.code,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					} as ProductionFlow,
				]);
			}

			return { previousProductionFlows };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousProductionFlows);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
