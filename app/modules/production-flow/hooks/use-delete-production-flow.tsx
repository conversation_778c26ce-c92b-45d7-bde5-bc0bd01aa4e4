import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";

import { productionFlowOptions } from "./production-flow-options";

export default function useDeleteProductionFlow() {
	const service = useService();
	const { productionFlow } = service;
	const queryClient = useQueryClient();
	const queryKey = productionFlowOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-production-flow"],
		mutationFn: (id: string) => AppRuntime.runPromise(productionFlow.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousProductionFlows = queryClient.getQueryData(queryKey);

			if (previousProductionFlows) {
				queryClient.setQueryData(
					queryKey,
					create(previousProductionFlows, (draft) => {
						const index = draft.findIndex((pf) => pf.id === id);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousProductionFlows };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousProductionFlows);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
