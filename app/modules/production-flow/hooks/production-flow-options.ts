import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const productionFlowOptions = ({ productionFlow }: serviceRegistry) =>
	queryOptions({
		queryKey: ["production-flows"],
		queryFn: () => AppRuntime.runPromise(productionFlow.getAll()),
	});

export const productionFlowOptionsById = ({ productionFlow }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["production-flows", id],
		queryFn: () => AppRuntime.runPromise(productionFlow.getById(id)),
	});

export const productionFlowWithActivitiesOptionsById = ({ productionFlow }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["production-flows", "with-activities", id],
		queryFn: () => AppRuntime.runPromise(productionFlow.getWithActivities(id)),
	});
