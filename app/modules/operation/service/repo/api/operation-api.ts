import { <PERSON>tt<PERSON><PERSON><PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { CreateOperation, UpdateOperation } from "../../model/operation";
import { OperationRepository } from "../../model/repository";
import {
	OperationFromApi,
	OperationListFromApi,
	CreateOperationApiFromCreateOperation,
	CreateOperationApiResponse,
	UpdateOperationApiFromUpdateOperation,
} from "./dto";

const baseUrl = "/v1/operations";

const makeOperationApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(OperationListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(OperationFromApi))),
		create: (operation: CreateOperation) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateOperationApiFromCreateOperation)(operation),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateOperationApiResponse))),
		update: (operation: UpdateOperation) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateOperationApiFromUpdateOperation)(operation),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate-code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const operationApiRepoLive = Layer.effect(OperationRepository, makeOperationApiRepo);
