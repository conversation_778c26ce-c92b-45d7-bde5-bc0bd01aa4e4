import { Schema } from "effect";
import { Operation, CreateOperation, UpdateOperation } from "../../model/operation";

export const OperationApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const OperationFromApi = Schema.transform(OperationApi, Operation, {
	strict: true,
	decode: (operationApi) => ({
		...operationApi,
		createdAt: operationApi.created_at,
		updatedAt: operationApi.updated_at,
		deletedAt: operationApi.deleted_at,
	}),
	encode: (operation) => ({
		...operation,
		created_at: operation.createdAt,
		updated_at: operation.updatedAt,
		deleted_at: operation.deletedAt,
	}),
});

export const OperationListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(OperationFromApi))),
	Schema.mutable(Schema.Array(Operation)),
	{
		strict: true,
		decode: (operationApiList) => (operationApiList ? operationApiList : []),
		encode: (operationList) => operationList,
	},
);

export const CreateOperationApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
});

export const CreateOperationApiFromCreateOperation = Schema.transform(
	CreateOperation,
	CreateOperationApi,
	{
		strict: true,
		decode: (createOperation) => createOperation,
		encode: (createOperationApi) => createOperationApi,
	},
);

export const UpdateOperationApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
});

export const UpdateOperationApiFromUpdateOperation = Schema.transform(
	UpdateOperation,
	UpdateOperationApi,
	{
		strict: true,
		decode: (updateOperation) => updateOperation,
		encode: (updateOperationApi) => updateOperationApi,
	},
);

export const CreateOperationApiResponse = Schema.String;
