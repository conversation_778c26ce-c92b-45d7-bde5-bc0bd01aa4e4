import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { Operation, CreateOperation, UpdateOperation } from "./operation";

export class OperationRepository extends Effect.Tag("OperationRepository")<
	OperationRepository,
	{
		readonly getAll: () => Effect.Effect<Operation[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Operation, AppError>;
		readonly create: (operation: CreateOperation) => Effect.Effect<string, AppError>;
		readonly update: (operation: UpdateOperation) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
