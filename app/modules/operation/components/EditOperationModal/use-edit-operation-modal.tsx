import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useUpdateOperation from "../../hooks/use-update-operation";
import type { Operation } from "../../service/model/operation";
import { CreateOperationSchema } from "../CreateOperationModal/schema";

export interface EditOperationModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	operation: Operation;
}

export default function useEditOperationModal({
	setIsOpen,
	operation,
}: EditOperationModalProps) {
	const { mutate } = useUpdateOperation();

	const form = useAppForm({
		defaultValues: {
			name: operation.name,
			code: operation.code,
		} as CreateOperationSchema,
		validators: {
			onChange: CreateOperationSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: operation.id,
					name: value.name,
					code: value.code,
				},
				{
					onSuccess: () => {
						toast.success("Operación actualizada");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
