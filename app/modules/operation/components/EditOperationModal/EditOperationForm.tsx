import { Hash, Tag } from "lucide-react";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { cn } from "~/core/utils/classes";
import useEditOperationModal, {
	type EditOperationModalProps,
} from "./use-edit-operation-modal";

export default function EditOperationForm({
	isOpen,
	setIsOpen,
	operation,
}: EditOperationModalProps) {
	const { operation: operationService } = useService();
	const { form, handleClose } = useEditOperationModal({
		isOpen,
		setIsOpen,
		operation,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Operación</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la operación"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "" || value === operation.code) {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(
												operationService.validateCode(value),
											);
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la operación"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Actualizar"
								className="btn btn-primary"
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
