import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { Operation } from "../../service/model/operation";
import { columns } from "./columns";

interface Props {
	operations: Operation[];
}

export default function Table({ operations }: Props) {
	const table = useReactTable({
		data: operations,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
