import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import type { Operation } from "../../service/model/operation";
import DeleteOperationModal from "../DeleteOperationModal";
import EditOperationModal from "../EditOperationModal";

const columnHelper = createColumnHelper<Operation>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => info.getValue(),
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const operation = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditOperationModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={operation.id}
					/>
					<DeleteOperationModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						operation={operation}
					/>
				</div>
			);
		},
	}),
];
