import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import useDeleteOperation from "../hooks/use-delete-operation";
import type { Operation } from "../service/model/operation";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	operation: Operation;
}

export default function DeleteOperationModal({
	isOpen,
	setIsOpen,
	operation,
}: Props) {
	const { mutate } = useDeleteOperation();

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar operación</h3>
				<p>¿Estás seguro de que quieres eliminar esta operación?</p>
				<p className="mt-2 text-gray-600 text-sm">
					Operación: {operation.name} ({operation.code})
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-primary"
						onClick={() => setIsOpen(false)}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={() => {
							mutate(operation.id, {
								onSuccess: () => {
									toast.success("Operación eliminada");
									setIsOpen(false);
								},
								onError: (error) => {
									console.log(error);
									toast.error("Error al eliminar operación");
								},
							});
						}}
					>
						Eliminar
					</button>
				</div>
			</div>
		</div>
	);
}
