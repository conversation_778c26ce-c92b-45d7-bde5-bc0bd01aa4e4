import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useCreateOperation from "../../hooks/use-create-operation";
import { CreateOperationSchema } from "./schema";

export interface CreateOperationModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	code: "",
} as CreateOperationSchema;

export default function useCreateOperationModal({
	setIsOpen,
}: CreateOperationModalProps) {
	const { mutate, isPending } = useCreateOperation();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateOperationSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onSuccess: () => {
					toast.success("Operación creada");
					handleClose();
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
