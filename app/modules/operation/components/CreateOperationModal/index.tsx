import { Hash, Tag } from "lucide-react";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { cn } from "~/core/utils/classes";
import type { CreateOperationModalProps } from "./use-create-modal";
import useCreateOperationModal from "./use-create-modal";

export default function CreateOperationModal({
	isOpen,
	setIsOpen,
}: CreateOperationModalProps) {
	const { operation } = useService();
	const { form, handleClose, isPending } = useCreateOperationModal({
		isOpen,
		setIsOpen,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Crear <PERSON></h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la operación"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(operation.validateCode(value));
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la operación"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
