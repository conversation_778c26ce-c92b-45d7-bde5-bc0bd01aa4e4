import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useCreateCategory from "../../hooks/use-create-category";
import useSelectedCategory from "../../hooks/use-selected-category";
import { CreateCategorySchema } from "./schema";

export interface CreateCategoryModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	code: "",
} as CreateCategorySchema;

export default function useCreateCategoryModal({
	setIsOpen,
}: CreateCategoryModalProps) {
	const { categoryId } = useSelectedCategory();

	const { mutate, isPending } = useCreateCategory();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateCategorySchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{ ...value, categoryId: categoryId },
				{
					onSuccess: () => {
						toast.success("Categoria creada");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
