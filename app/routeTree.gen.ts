/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as AuthedRouteImport } from './routes/_authed/route'
import { Route as IndexImport } from './routes/index'
import { Route as AuthedAdminRouteImport } from './routes/_authed/admin/route'
import { Route as AuthedAdminIndexImport } from './routes/_authed/admin/index'
import { Route as AuthedAdminSecurityUsersIndexImport } from './routes/_authed/admin/security/users/index'
import { Route as AuthedAdminProductsSuppliersIndexImport } from './routes/_authed/admin/products/suppliers/index'
import { Route as AuthedAdminProductsRawMaterialsIndexImport } from './routes/_authed/admin/products/raw-materials/index'
import { Route as AuthedAdminProductsProductsIndexImport } from './routes/_authed/admin/products/products/index'
import { Route as AuthedAdminProductsMeasurementUnitsIndexImport } from './routes/_authed/admin/products/measurement-units/index'
import { Route as AuthedAdminProductsMaterialsIndexImport } from './routes/_authed/admin/products/materials/index'
import { Route as AuthedAdminProductsDeviceMaterialsIndexImport } from './routes/_authed/admin/products/device-materials/index'
import { Route as AuthedAdminProductsCategoriesIndexImport } from './routes/_authed/admin/products/categories/index'
import { Route as AuthedAdminProductsBrandsIndexImport } from './routes/_authed/admin/products/brands/index'
import { Route as AuthedAdminManufactureWorkAreaIndexImport } from './routes/_authed/admin/manufacture/work-area/index'
import { Route as AuthedAdminManufactureProductionFlowIndexImport } from './routes/_authed/admin/manufacture/production-flow/index'
import { Route as AuthedAdminManufactureOperationsIndexImport } from './routes/_authed/admin/manufacture/operations/index'
import { Route as AuthedAdminManufactureProductionFlowCreateImport } from './routes/_authed/admin/manufacture/production-flow/create'
import { Route as AuthedAdminManufactureProductionFlowEditIdImport } from './routes/_authed/admin/manufacture/production-flow/edit.$id'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthedRouteRoute = AuthedRouteImport.update({
  id: '/_authed',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AuthedAdminRouteRoute = AuthedAdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthedRouteRoute,
} as any)

const AuthedAdminIndexRoute = AuthedAdminIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthedAdminRouteRoute,
} as any)

const AuthedAdminSecurityUsersIndexRoute =
  AuthedAdminSecurityUsersIndexImport.update({
    id: '/security/users/',
    path: '/security/users/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsSuppliersIndexRoute =
  AuthedAdminProductsSuppliersIndexImport.update({
    id: '/products/suppliers/',
    path: '/products/suppliers/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsRawMaterialsIndexRoute =
  AuthedAdminProductsRawMaterialsIndexImport.update({
    id: '/products/raw-materials/',
    path: '/products/raw-materials/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsProductsIndexRoute =
  AuthedAdminProductsProductsIndexImport.update({
    id: '/products/products/',
    path: '/products/products/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsMeasurementUnitsIndexRoute =
  AuthedAdminProductsMeasurementUnitsIndexImport.update({
    id: '/products/measurement-units/',
    path: '/products/measurement-units/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsMaterialsIndexRoute =
  AuthedAdminProductsMaterialsIndexImport.update({
    id: '/products/materials/',
    path: '/products/materials/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsDeviceMaterialsIndexRoute =
  AuthedAdminProductsDeviceMaterialsIndexImport.update({
    id: '/products/device-materials/',
    path: '/products/device-materials/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsCategoriesIndexRoute =
  AuthedAdminProductsCategoriesIndexImport.update({
    id: '/products/categories/',
    path: '/products/categories/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminProductsBrandsIndexRoute =
  AuthedAdminProductsBrandsIndexImport.update({
    id: '/products/brands/',
    path: '/products/brands/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminManufactureWorkAreaIndexRoute =
  AuthedAdminManufactureWorkAreaIndexImport.update({
    id: '/manufacture/work-area/',
    path: '/manufacture/work-area/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminManufactureProductionFlowIndexRoute =
  AuthedAdminManufactureProductionFlowIndexImport.update({
    id: '/manufacture/production-flow/',
    path: '/manufacture/production-flow/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminManufactureOperationsIndexRoute =
  AuthedAdminManufactureOperationsIndexImport.update({
    id: '/manufacture/operations/',
    path: '/manufacture/operations/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminManufactureProductionFlowCreateRoute =
  AuthedAdminManufactureProductionFlowCreateImport.update({
    id: '/manufacture/production-flow/create',
    path: '/manufacture/production-flow/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

const AuthedAdminManufactureProductionFlowEditIdRoute =
  AuthedAdminManufactureProductionFlowEditIdImport.update({
    id: '/manufacture/production-flow/edit/$id',
    path: '/manufacture/production-flow/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_authed': {
      id: '/_authed'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthedRouteImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/_authed/admin': {
      id: '/_authed/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthedAdminRouteImport
      parentRoute: typeof AuthedRouteImport
    }
    '/_authed/admin/': {
      id: '/_authed/admin/'
      path: '/'
      fullPath: '/admin/'
      preLoaderRoute: typeof AuthedAdminIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/manufacture/production-flow/create': {
      id: '/_authed/admin/manufacture/production-flow/create'
      path: '/manufacture/production-flow/create'
      fullPath: '/admin/manufacture/production-flow/create'
      preLoaderRoute: typeof AuthedAdminManufactureProductionFlowCreateImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/manufacture/operations/': {
      id: '/_authed/admin/manufacture/operations/'
      path: '/manufacture/operations'
      fullPath: '/admin/manufacture/operations'
      preLoaderRoute: typeof AuthedAdminManufactureOperationsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/manufacture/production-flow/': {
      id: '/_authed/admin/manufacture/production-flow/'
      path: '/manufacture/production-flow'
      fullPath: '/admin/manufacture/production-flow'
      preLoaderRoute: typeof AuthedAdminManufactureProductionFlowIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/manufacture/work-area/': {
      id: '/_authed/admin/manufacture/work-area/'
      path: '/manufacture/work-area'
      fullPath: '/admin/manufacture/work-area'
      preLoaderRoute: typeof AuthedAdminManufactureWorkAreaIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/brands/': {
      id: '/_authed/admin/products/brands/'
      path: '/products/brands'
      fullPath: '/admin/products/brands'
      preLoaderRoute: typeof AuthedAdminProductsBrandsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/categories/': {
      id: '/_authed/admin/products/categories/'
      path: '/products/categories'
      fullPath: '/admin/products/categories'
      preLoaderRoute: typeof AuthedAdminProductsCategoriesIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/device-materials/': {
      id: '/_authed/admin/products/device-materials/'
      path: '/products/device-materials'
      fullPath: '/admin/products/device-materials'
      preLoaderRoute: typeof AuthedAdminProductsDeviceMaterialsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/materials/': {
      id: '/_authed/admin/products/materials/'
      path: '/products/materials'
      fullPath: '/admin/products/materials'
      preLoaderRoute: typeof AuthedAdminProductsMaterialsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/measurement-units/': {
      id: '/_authed/admin/products/measurement-units/'
      path: '/products/measurement-units'
      fullPath: '/admin/products/measurement-units'
      preLoaderRoute: typeof AuthedAdminProductsMeasurementUnitsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/products/': {
      id: '/_authed/admin/products/products/'
      path: '/products/products'
      fullPath: '/admin/products/products'
      preLoaderRoute: typeof AuthedAdminProductsProductsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/raw-materials/': {
      id: '/_authed/admin/products/raw-materials/'
      path: '/products/raw-materials'
      fullPath: '/admin/products/raw-materials'
      preLoaderRoute: typeof AuthedAdminProductsRawMaterialsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/products/suppliers/': {
      id: '/_authed/admin/products/suppliers/'
      path: '/products/suppliers'
      fullPath: '/admin/products/suppliers'
      preLoaderRoute: typeof AuthedAdminProductsSuppliersIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/security/users/': {
      id: '/_authed/admin/security/users/'
      path: '/security/users'
      fullPath: '/admin/security/users'
      preLoaderRoute: typeof AuthedAdminSecurityUsersIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/manufacture/production-flow/edit/$id': {
      id: '/_authed/admin/manufacture/production-flow/edit/$id'
      path: '/manufacture/production-flow/edit/$id'
      fullPath: '/admin/manufacture/production-flow/edit/$id'
      preLoaderRoute: typeof AuthedAdminManufactureProductionFlowEditIdImport
      parentRoute: typeof AuthedAdminRouteImport
    }
  }
}

// Create and export the route tree

interface AuthedAdminRouteRouteChildren {
  AuthedAdminIndexRoute: typeof AuthedAdminIndexRoute
  AuthedAdminManufactureProductionFlowCreateRoute: typeof AuthedAdminManufactureProductionFlowCreateRoute
  AuthedAdminManufactureOperationsIndexRoute: typeof AuthedAdminManufactureOperationsIndexRoute
  AuthedAdminManufactureProductionFlowIndexRoute: typeof AuthedAdminManufactureProductionFlowIndexRoute
  AuthedAdminManufactureWorkAreaIndexRoute: typeof AuthedAdminManufactureWorkAreaIndexRoute
  AuthedAdminProductsBrandsIndexRoute: typeof AuthedAdminProductsBrandsIndexRoute
  AuthedAdminProductsCategoriesIndexRoute: typeof AuthedAdminProductsCategoriesIndexRoute
  AuthedAdminProductsDeviceMaterialsIndexRoute: typeof AuthedAdminProductsDeviceMaterialsIndexRoute
  AuthedAdminProductsMaterialsIndexRoute: typeof AuthedAdminProductsMaterialsIndexRoute
  AuthedAdminProductsMeasurementUnitsIndexRoute: typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  AuthedAdminProductsProductsIndexRoute: typeof AuthedAdminProductsProductsIndexRoute
  AuthedAdminProductsRawMaterialsIndexRoute: typeof AuthedAdminProductsRawMaterialsIndexRoute
  AuthedAdminProductsSuppliersIndexRoute: typeof AuthedAdminProductsSuppliersIndexRoute
  AuthedAdminSecurityUsersIndexRoute: typeof AuthedAdminSecurityUsersIndexRoute
  AuthedAdminManufactureProductionFlowEditIdRoute: typeof AuthedAdminManufactureProductionFlowEditIdRoute
}

const AuthedAdminRouteRouteChildren: AuthedAdminRouteRouteChildren = {
  AuthedAdminIndexRoute: AuthedAdminIndexRoute,
  AuthedAdminManufactureProductionFlowCreateRoute:
    AuthedAdminManufactureProductionFlowCreateRoute,
  AuthedAdminManufactureOperationsIndexRoute:
    AuthedAdminManufactureOperationsIndexRoute,
  AuthedAdminManufactureProductionFlowIndexRoute:
    AuthedAdminManufactureProductionFlowIndexRoute,
  AuthedAdminManufactureWorkAreaIndexRoute:
    AuthedAdminManufactureWorkAreaIndexRoute,
  AuthedAdminProductsBrandsIndexRoute: AuthedAdminProductsBrandsIndexRoute,
  AuthedAdminProductsCategoriesIndexRoute:
    AuthedAdminProductsCategoriesIndexRoute,
  AuthedAdminProductsDeviceMaterialsIndexRoute:
    AuthedAdminProductsDeviceMaterialsIndexRoute,
  AuthedAdminProductsMaterialsIndexRoute:
    AuthedAdminProductsMaterialsIndexRoute,
  AuthedAdminProductsMeasurementUnitsIndexRoute:
    AuthedAdminProductsMeasurementUnitsIndexRoute,
  AuthedAdminProductsProductsIndexRoute: AuthedAdminProductsProductsIndexRoute,
  AuthedAdminProductsRawMaterialsIndexRoute:
    AuthedAdminProductsRawMaterialsIndexRoute,
  AuthedAdminProductsSuppliersIndexRoute:
    AuthedAdminProductsSuppliersIndexRoute,
  AuthedAdminSecurityUsersIndexRoute: AuthedAdminSecurityUsersIndexRoute,
  AuthedAdminManufactureProductionFlowEditIdRoute:
    AuthedAdminManufactureProductionFlowEditIdRoute,
}

const AuthedAdminRouteRouteWithChildren =
  AuthedAdminRouteRoute._addFileChildren(AuthedAdminRouteRouteChildren)

interface AuthedRouteRouteChildren {
  AuthedAdminRouteRoute: typeof AuthedAdminRouteRouteWithChildren
}

const AuthedRouteRouteChildren: AuthedRouteRouteChildren = {
  AuthedAdminRouteRoute: AuthedAdminRouteRouteWithChildren,
}

const AuthedRouteRouteWithChildren = AuthedRouteRoute._addFileChildren(
  AuthedRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminRouteRouteWithChildren
  '/admin/': typeof AuthedAdminIndexRoute
  '/admin/manufacture/production-flow/create': typeof AuthedAdminManufactureProductionFlowCreateRoute
  '/admin/manufacture/operations': typeof AuthedAdminManufactureOperationsIndexRoute
  '/admin/manufacture/production-flow': typeof AuthedAdminManufactureProductionFlowIndexRoute
  '/admin/manufacture/work-area': typeof AuthedAdminManufactureWorkAreaIndexRoute
  '/admin/products/brands': typeof AuthedAdminProductsBrandsIndexRoute
  '/admin/products/categories': typeof AuthedAdminProductsCategoriesIndexRoute
  '/admin/products/device-materials': typeof AuthedAdminProductsDeviceMaterialsIndexRoute
  '/admin/products/materials': typeof AuthedAdminProductsMaterialsIndexRoute
  '/admin/products/measurement-units': typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  '/admin/products/products': typeof AuthedAdminProductsProductsIndexRoute
  '/admin/products/raw-materials': typeof AuthedAdminProductsRawMaterialsIndexRoute
  '/admin/products/suppliers': typeof AuthedAdminProductsSuppliersIndexRoute
  '/admin/security/users': typeof AuthedAdminSecurityUsersIndexRoute
  '/admin/manufacture/production-flow/edit/$id': typeof AuthedAdminManufactureProductionFlowEditIdRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminIndexRoute
  '/admin/manufacture/production-flow/create': typeof AuthedAdminManufactureProductionFlowCreateRoute
  '/admin/manufacture/operations': typeof AuthedAdminManufactureOperationsIndexRoute
  '/admin/manufacture/production-flow': typeof AuthedAdminManufactureProductionFlowIndexRoute
  '/admin/manufacture/work-area': typeof AuthedAdminManufactureWorkAreaIndexRoute
  '/admin/products/brands': typeof AuthedAdminProductsBrandsIndexRoute
  '/admin/products/categories': typeof AuthedAdminProductsCategoriesIndexRoute
  '/admin/products/device-materials': typeof AuthedAdminProductsDeviceMaterialsIndexRoute
  '/admin/products/materials': typeof AuthedAdminProductsMaterialsIndexRoute
  '/admin/products/measurement-units': typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  '/admin/products/products': typeof AuthedAdminProductsProductsIndexRoute
  '/admin/products/raw-materials': typeof AuthedAdminProductsRawMaterialsIndexRoute
  '/admin/products/suppliers': typeof AuthedAdminProductsSuppliersIndexRoute
  '/admin/security/users': typeof AuthedAdminSecurityUsersIndexRoute
  '/admin/manufacture/production-flow/edit/$id': typeof AuthedAdminManufactureProductionFlowEditIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_authed': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/_authed/admin': typeof AuthedAdminRouteRouteWithChildren
  '/_authed/admin/': typeof AuthedAdminIndexRoute
  '/_authed/admin/manufacture/production-flow/create': typeof AuthedAdminManufactureProductionFlowCreateRoute
  '/_authed/admin/manufacture/operations/': typeof AuthedAdminManufactureOperationsIndexRoute
  '/_authed/admin/manufacture/production-flow/': typeof AuthedAdminManufactureProductionFlowIndexRoute
  '/_authed/admin/manufacture/work-area/': typeof AuthedAdminManufactureWorkAreaIndexRoute
  '/_authed/admin/products/brands/': typeof AuthedAdminProductsBrandsIndexRoute
  '/_authed/admin/products/categories/': typeof AuthedAdminProductsCategoriesIndexRoute
  '/_authed/admin/products/device-materials/': typeof AuthedAdminProductsDeviceMaterialsIndexRoute
  '/_authed/admin/products/materials/': typeof AuthedAdminProductsMaterialsIndexRoute
  '/_authed/admin/products/measurement-units/': typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  '/_authed/admin/products/products/': typeof AuthedAdminProductsProductsIndexRoute
  '/_authed/admin/products/raw-materials/': typeof AuthedAdminProductsRawMaterialsIndexRoute
  '/_authed/admin/products/suppliers/': typeof AuthedAdminProductsSuppliersIndexRoute
  '/_authed/admin/security/users/': typeof AuthedAdminSecurityUsersIndexRoute
  '/_authed/admin/manufacture/production-flow/edit/$id': typeof AuthedAdminManufactureProductionFlowEditIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/login'
    | '/admin'
    | '/admin/'
    | '/admin/manufacture/production-flow/create'
    | '/admin/manufacture/operations'
    | '/admin/manufacture/production-flow'
    | '/admin/manufacture/work-area'
    | '/admin/products/brands'
    | '/admin/products/categories'
    | '/admin/products/device-materials'
    | '/admin/products/materials'
    | '/admin/products/measurement-units'
    | '/admin/products/products'
    | '/admin/products/raw-materials'
    | '/admin/products/suppliers'
    | '/admin/security/users'
    | '/admin/manufacture/production-flow/edit/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/login'
    | '/admin'
    | '/admin/manufacture/production-flow/create'
    | '/admin/manufacture/operations'
    | '/admin/manufacture/production-flow'
    | '/admin/manufacture/work-area'
    | '/admin/products/brands'
    | '/admin/products/categories'
    | '/admin/products/device-materials'
    | '/admin/products/materials'
    | '/admin/products/measurement-units'
    | '/admin/products/products'
    | '/admin/products/raw-materials'
    | '/admin/products/suppliers'
    | '/admin/security/users'
    | '/admin/manufacture/production-flow/edit/$id'
  id:
    | '__root__'
    | '/'
    | '/_authed'
    | '/login'
    | '/_authed/admin'
    | '/_authed/admin/'
    | '/_authed/admin/manufacture/production-flow/create'
    | '/_authed/admin/manufacture/operations/'
    | '/_authed/admin/manufacture/production-flow/'
    | '/_authed/admin/manufacture/work-area/'
    | '/_authed/admin/products/brands/'
    | '/_authed/admin/products/categories/'
    | '/_authed/admin/products/device-materials/'
    | '/_authed/admin/products/materials/'
    | '/_authed/admin/products/measurement-units/'
    | '/_authed/admin/products/products/'
    | '/_authed/admin/products/raw-materials/'
    | '/_authed/admin/products/suppliers/'
    | '/_authed/admin/security/users/'
    | '/_authed/admin/manufacture/production-flow/edit/$id'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthedRouteRoute: typeof AuthedRouteRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthedRouteRoute: AuthedRouteRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_authed",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_authed": {
      "filePath": "_authed/route.tsx",
      "children": [
        "/_authed/admin"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_authed/admin": {
      "filePath": "_authed/admin/route.tsx",
      "parent": "/_authed",
      "children": [
        "/_authed/admin/",
        "/_authed/admin/manufacture/production-flow/create",
        "/_authed/admin/manufacture/operations/",
        "/_authed/admin/manufacture/production-flow/",
        "/_authed/admin/manufacture/work-area/",
        "/_authed/admin/products/brands/",
        "/_authed/admin/products/categories/",
        "/_authed/admin/products/device-materials/",
        "/_authed/admin/products/materials/",
        "/_authed/admin/products/measurement-units/",
        "/_authed/admin/products/products/",
        "/_authed/admin/products/raw-materials/",
        "/_authed/admin/products/suppliers/",
        "/_authed/admin/security/users/",
        "/_authed/admin/manufacture/production-flow/edit/$id"
      ]
    },
    "/_authed/admin/": {
      "filePath": "_authed/admin/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/manufacture/production-flow/create": {
      "filePath": "_authed/admin/manufacture/production-flow/create.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/manufacture/operations/": {
      "filePath": "_authed/admin/manufacture/operations/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/manufacture/production-flow/": {
      "filePath": "_authed/admin/manufacture/production-flow/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/manufacture/work-area/": {
      "filePath": "_authed/admin/manufacture/work-area/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/brands/": {
      "filePath": "_authed/admin/products/brands/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/categories/": {
      "filePath": "_authed/admin/products/categories/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/device-materials/": {
      "filePath": "_authed/admin/products/device-materials/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/materials/": {
      "filePath": "_authed/admin/products/materials/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/measurement-units/": {
      "filePath": "_authed/admin/products/measurement-units/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/products/": {
      "filePath": "_authed/admin/products/products/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/raw-materials/": {
      "filePath": "_authed/admin/products/raw-materials/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/products/suppliers/": {
      "filePath": "_authed/admin/products/suppliers/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/security/users/": {
      "filePath": "_authed/admin/security/users/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/manufacture/production-flow/edit/$id": {
      "filePath": "_authed/admin/manufacture/production-flow/edit.$id.tsx",
      "parent": "/_authed/admin"
    }
  }
}
ROUTE_MANIFEST_END */
