{"name": "fhyona-v2-frontend", "module": "index.ts", "type": "module", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start"}, "private": true, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/bun": "latest", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "daisyui": "^5.0.6", "vite-tsconfig-paths": "^5.1.4"}, "peerDependencies": {"typescript": "^5.8.2"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@effect/platform": "^0.82.6", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-form": "^1.11.3", "@tanstack/react-query": "^5.76.2", "@tanstack/react-query-devtools": "^5.76.2", "@tanstack/react-router": "^1.120.9", "@tanstack/react-router-devtools": "^1.120.9", "@tanstack/react-router-with-query": "^1.120.9", "@tanstack/react-start": "^1.120.9", "@tanstack/react-store": "^0.7.0", "@tanstack/react-table": "^8.21.3", "clsx": "^2.1.1", "downshift": "^9.0.9", "effect": "^3.15.3", "lucide-react": "^0.511.0", "mutative": "^1.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-toastify": "^11.0.5", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "use-mutative": "^1.3.0", "valibot": "^1.1.0", "vinxi": "^0.5.6"}}